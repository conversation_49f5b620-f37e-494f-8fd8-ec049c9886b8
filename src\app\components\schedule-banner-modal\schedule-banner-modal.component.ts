import { Component, EventEmitter, Output } from '@angular/core';
import { FormControl, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import moment from 'moment';

@Component({
  selector: 'app-schedule-banner-modal',
  templateUrl: './schedule-banner-modal.component.html',
  styleUrl: './schedule-banner-modal.component.css'
})
export class ScheduleBannerModalComponent {
  @Output() onScheduleConfirm = new EventEmitter<moment.Moment>();

  scheduleForm = new FormGroup({
    date: new FormControl<Date | null>(null, [Validators.required, this.futureDateValidator]),
    time: new FormControl<string>('', [Validators.required, this.futureTimeValidator.bind(this)])
  });

  constructor(
    private _dialogRef: MatDialogRef<ScheduleBannerModalComponent>
  ) {}

  // Validator to ensure date is in the future
  futureDateValidator(control: AbstractControl): { [key: string]: any } | null {
    if (!control.value) return null;
    
    const selectedDate = moment(control.value).startOf('day');
    const today = moment().startOf('day');
    
    if (selectedDate.isBefore(today)) {
      return { pastDate: true };
    }
    
    return null;
  }

  // Validator to ensure time is in the future for today's date
  futureTimeValidator(control: AbstractControl): { [key: string]: any } | null {
    if (!control.value || !this.scheduleForm?.get('date')?.value) return null;
    
    const selectedDate = moment(this.scheduleForm.get('date')?.value).startOf('day');
    const today = moment().startOf('day');
    
    // Only validate time if the selected date is today
    if (selectedDate.isSame(today)) {
      const [hours, minutes] = control.value.split(':').map(Number);
      const selectedDateTime = moment().hours(hours).minutes(minutes).seconds(0);
      const now = moment();
      
      if (selectedDateTime.isBefore(now)) {
        return { pastTime: true };
      }
    }
    
    return null;
  }

  onPublish() {
    if (this.scheduleForm.valid) {
      const date = this.scheduleForm.get('date')?.value;
      const time = this.scheduleForm.get('time')?.value;
      
      if (date && time) {
        const [hours, minutes] = time.split(':').map(Number);
        const scheduledDateTime = moment(date).hours(hours).minutes(minutes).seconds(0);
        
        this.onScheduleConfirm.emit(scheduledDateTime);
        this.onClose();
      }
    }
  }

  onCancel() {
    this.onClose();
  }

  onClose() {
    this._dialogRef.close();
  }
}
