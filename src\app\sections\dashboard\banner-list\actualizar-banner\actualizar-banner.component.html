<div class="app">
  <p class="texto-banner">{{ texto }}</p>
  <div class="top-section">
    <form [formGroup]="bannerForm" (ngSubmit)="onSubmit($event)" class="inputs">
      <div class="form-content">
      <div class="image-upload-wrapper">  
        <!-- Zona de carga de imagen móvil -->
        <div class="image-upload-container">
          <p class="image-upload-text">Imagen móvil (360x248px)</p>  
          <label for="imageUploadMobile">
            <div class="icon" 
              (click)="activateIconMobile()" 
              (drop)="onDropMobile($event); activateIconMobile()" 
              (dragover)="onDragOverMobile($event)" 
              (dragleave)="onDragLeaveMobile($event)"
            >
              <!-- Input para cargar la imagen móvil -->
              @if (showInputs){ 
                <input type="file" 
                    id="imageUploadMobile" 
                    name="imageUploadMobile"
                    [src]="bannerImageMobile" 
                    (change)="handleFileInputMobile($event)" 
                    style="display:none;"
                />
              }
              <!-- Si el modo es 'Crear banner', muestra el ícono o la imagen cargada -->
              @if (modo === 'Crear banner' && !bannerImageMobile){
                <mat-icon  [svgIcon]="iconoMobile" style="width: 126px;height: 126px;"></mat-icon>
              }
              <!-- Si el modo es 'Crear banner', muestra la imagen si existe -->
              @if (modo === 'Crear banner' && bannerImageMobile){ 
                <img  [src]="bannerImageMobile" alt="Imagen del mobile" style="width: 126px;height: 126px;">
              }
              <!-- Si el modo es 'Crear banner', muestra 'Agregar imagen' -->
              @if (mostrarTextoIconoMobile && modo === 'Crear banner' && showInputs){  
                <p  class="texto_imagen">{{ fileNameMobile ? fileNameMobile : 'Agregar imagen' }}</p>
              }
            </div>
          </label>
          @if (!imageLoadedMobile && formSubmitted){
              <div  class="error">
                Debe cargar la imagen para móvil.
              </div>
          }
        </div>


        <!-- Zona de carga de imagen web -->
        <div class="image-upload-container">
          <p class="image-upload-text">Imagen web (1200x344px)</p>
          <label for="imageUploadWeb">
            <div class="icon" 
              (click)="activateIconWeb()" 
              (drop)="onDropWeb($event); activateIconWeb()" 
              (dragover)="onDragOverWeb($event)" 
              (dragleave)="onDragLeaveWeb($event)"
            >
              <!-- Input para cargar la imagen web -->
              @if (showInputs) { 
                <input type="file" 
                    id="imageUploadWeb" 
                    name="imageUploadWeb"
                    [src]="bannerImageWeb" 
                    (change)="handleFileInputWeb($event)" 
                    style="display:none;"
                />
              }
              <!-- Si el modo es 'Crear banner', muestra el ícono o la imagen cargada -->
              @if (modo === 'Crear banner' && !bannerImageWeb){
                <mat-icon  [svgIcon]="iconoWeb" style="width: 126px;height: 126px;"></mat-icon>
              }
              <!-- Si el modo es 'Crear banner', muestra la imagen si existe -->
              @if (modo === 'Crear banner' && bannerImageWeb){  
                <img  [src]="bannerImageWeb" alt="Imagen de la web" style="width: 126px;height: 126px;">
              }  
              <!-- Si el modo es 'Crear banner', muestra 'Agregar imagen' -->
              @if (mostrarTextoIconoWeb && modo === 'Crear banner' && showInputs){  
                <p  class="texto_imagen">{{ fileNameWeb ? fileNameWeb : 'Agregar imagen' }}</p>
              }
            </div>
          </label>
          @if (!imageLoadedWeb && formSubmitted){
              <div  class="error">
                  Debe cargar la imagen para web.
              </div>
          }
        </div>

      </div>
        <!-- Otros campos del formulario -->
        <div class="other-fields">
          <!-- Título del banner -->
          <div class="input-field">
            <label for="banner-title" class="input-titulo">Nombre</label>
            @if (showInputs){
              <input formControlName="bannerTitle" 
                  type="text" id="banner-title" 
                  name="banner-title" 
                  [value]="bannerTitleValue"
                  placeholder="Ingresa nombre de imagen" 
                  class="input-titulo" 
                  (keydown.enter)="$event.preventDefault()" 
                  (focus)="activateIcon()" required
              >
            }
            @if (bannerForm.controls['bannerTitle'].invalid && formSubmitted){
              <div  class="error">
                El campo debe contener el nombre de imagen
              </div>
            }                
          </div>
          <!-- Valor del título del banner -->
          @if (!showInputs){
            <div class="valor-procesado" >
              {{ bannerForm.get('bannerTitle')?.value }}
            </div>
          }
          <!-- Url banner -->
          <div class="input-field-textarea">
            <label for="banner-url" class="input-titulo">URL de la imagen</label>
            @if (showInputs) {
                <textarea formControlName="bannerUrl" 
                  id="banner-url" 
                  name="banner-url" 
                  [value]="bannerUrlValue"
                  placeholder="Ingresa URL de la imagen" 
                  pattern="https?://.+"
                  class="input-titulo" 
                  (keydown.enter)="$event.preventDefault()" 
                  (focus)="activateIcon()" required
                >    
                </textarea>
            }
            @if (bannerForm.controls['bannerUrl'].invalid && formSubmitted){
                <div  class="error">  
                  El campo debe contener una URL válida para la imagen.
                </div>
            }
          </div>
          <!-- Valor del ulr del banner -->
          @if (!showInputs){ 
            <div class="valor-procesado">
              {{ bannerForm.get('bannerUrl')?.value }}
            </div>    
          } 
          <div class="input-field">
            <label for="banner-text" class="input-titulo">Texto principal (máximo 60 caracteres)</label>
            <input formControlName="banner_text" type="text" id="banner-text" name="banner_text"
            placeholder="Ingresa el texto del banner" class="input-titulo" (keydown.enter)="$event.preventDefault()">
          </div>
          <div class="input-field">
            <label for="banner-action-text" class="input-titulo">Texto del botón (máximo 20 caracteres)</label>
            <input formControlName="banner_action_text" type="text" id="banner-action-text" name="banner_action_text"
            placeholder="Ingresa el texto del botón en banner" class="input-titulo" (keydown.enter)="$event.preventDefault()">
          </div>
          <div class="area-button-submit">
            @if (showInputs){
                <button  
                  class="form-submit"
                  type="submit"     
                  [disabled]="!bannerForm.valid || !imageLoadedMobile || !imageLoadedWeb"
                >
                  {{ showInputs ? 'Guardar' : 'Editar Playlist' }}
                </button>
            }
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
