import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DashboardComponent } from '../dashboard.component';
import { OverviewComponent } from '../overview/overview.component';
import jwtCheckGuard from '../../../guards/jwt-check.guard';
import { ReproductionsComponent } from '../reproductions/reproductions.component';
import { AlbumDetailComponent } from '../album-detail/album-detail.component';
import { CargaCancionesComponent } from '../carga-canciones/carga-canciones.component';
import { roleAccessGuard } from '../../../guards/role-access.guard';
import { Roles } from '../../../types/auth';
import { PlayListComponent } from '../play-list/play-list.component';
import { ActualizarPlaylistComponent } from '../play-list/actualizar-playlist/actualizar-playlist.component';
import { BannerListComponent } from '../banner-list/banner-list.component';
import { CreateBannerComponent } from '../banner-list/create-banner/create-banner.component';
import { RadioListComponent } from '../radio/components/radio-list/radio-list.component';
import { CreateRadioComponent } from '../radio/components/create-radio/create-radio.component';
import { UpdateRadioComponent } from '../radio/components/update-radio/update-radio.component';
import { ArtistReproductionsComponent } from '../artist-reproductions/artist-reproductions.component';
import { ActualizarBannerComponent } from '../banner-list/actualizar-banner/actualizar-banner.component';
import { RecordLabelReproductionsComponent } from '../record-label/record-label-reproductions/record-label-reproductions.component';

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: DashboardComponent,
        children: [
          {
            path: 'overview',
            component: OverviewComponent,
          },
          {
            path: 'reproductions',
            children:[
              {
                path: '',
                component: ReproductionsComponent,
              },
              {
                path: 'album-detail/:albumId',
                component: AlbumDetailComponent,
              },
            ]
          },      
          {
            path: '**',
            redirectTo: 'overview',
            pathMatch: 'full',
          },
        ],
        canMatch:[roleAccessGuard],
        data:{role:Roles.COMMERCIAL},
        canActivateChild:[jwtCheckGuard]
      },
      {
        path: '',
        component: DashboardComponent,
        children: [
          {
            path: 'carga-canciones',
            component: CargaCancionesComponent,
          },
          {
            path: 'banner-list',
            children:[
              {
                path: '',
                component: BannerListComponent,
              },
              {
                path: 'new',
                component: CreateBannerComponent,
              }, 
            ],
          },
          {
            path: 'play-list',
            children:[
              {
                path: '',
                component: PlayListComponent,
              },
              {
                path: 'new',
                component: ActualizarPlaylistComponent,
              },
              {
                path: 'update/:playlist_id',
                component: ActualizarPlaylistComponent,
              },
            ],
          }, 
          {
            path:'radio',
            children:[
              { path:'',component:RadioListComponent},
              { path:'new',component:CreateRadioComponent},
              { path:'update/:radio_id',component:UpdateRadioComponent},
            ]
          },       
          {
            path: '**',
            redirectTo: 'carga-canciones',
            pathMatch: 'full',
          },
        ],
        canMatch:[roleAccessGuard],
        data:{role:Roles.ADMIN},
        canActivateChild:[jwtCheckGuard]
      },
      {
        path: '',
        component: DashboardComponent,
        children: [
          {
            path:'reproductions',
            children:[
              {
                path: '',
                component: ArtistReproductionsComponent,
              },
              {
                path: 'album-detail/:albumId',
                component: AlbumDetailComponent,
              },
            ]
          },
          {
            path: '**',
            redirectTo: 'reproductions',
            pathMatch: 'full',
          },
        ],
        canMatch:[roleAccessGuard],
        data:{role:Roles.ARTIST},
        canActivateChild:[jwtCheckGuard]
      },
      {
        path: '',
        component: DashboardComponent,
        children: [
          {
            path:'reproductions',
            component: RecordLabelReproductionsComponent
          },
          {
            path: '**',
            redirectTo: 'reproductions',
            pathMatch: 'full',
          },
        ],
        canMatch:[roleAccessGuard],
        data:{role:Roles.RECORD_LABEL},
        canActivateChild:[jwtCheckGuard]
      },
      {
        path: '**',
        redirectTo: '',
        pathMatch: 'full',
      },
    ]),
  ],
  exports:[RouterModule],
})
export class  DashboardRoutingModule {}
