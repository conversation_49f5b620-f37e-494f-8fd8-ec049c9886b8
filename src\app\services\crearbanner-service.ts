import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { CreateBannerDto } from '../types/banner';

// Definición de la interfaz Banner
export interface Banner {
  banner_id: number;
  name: string;
  url: string;
}


// Definición de la interfaz Response
export interface Response {
  httpCode: number;
  systemMessage: string;
  bannerId: number;
  banner: Banner;
}

// Definición de constantes para los códigos de estado HTTP
const HTTP_STATUS_OK = 200;
const HTTP_STATUS_CREATED = 201;
const HTTP_STATUS_BAD_REQUEST = 400;
const HTTP_STATUS_UNAUTHORIZED = 401;
const HTTP_STATUS_FORBIDDEN = 403;
const HTTP_STATUS_NOT_FOUND = 404;
const HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;

@Injectable({
  providedIn: 'root'
})
export class CrearBannerService {
    
  private url = `${environment.apiUrl}/banner`;

  constructor(private http: HttpClient) { }

  // Uso de la interfaz Response para definir el tipo de retorno
  createBanner(data:CreateBannerDto): Observable<HttpResponse<Response>> {
    const formData = new FormData();
    formData.append('bannerWeb', data.bannerWeb);
    formData.append('bannerWebExt', data.bannerWebExt);
    formData.append('bannerMob', data.bannerMob);
    formData.append('nombre', data.nombre);
    formData.append('url', data.url);
    formData.append('position', data.position.toString());
    formData.append('state', data.state.toString());
    formData.append('texto_banner', data.texto_banner);
    formData.append('texto_boton_banner',data.texto_boton_banner);

    return this.http.post<Response>(this.url, formData, {
      headers: {
        'accept': '*/*'
      },
      observe: 'response'
    }).pipe(
      map(response => {
        // Aquí puedes manejar la respuesta
        if (response.status === HTTP_STATUS_OK) {
          console.log('Exito creando el banner:', response.body);
        }
        return response;
      }),
      catchError(error => {
        let errorMessage = 'Ha ocurrido un error desconocido';
      
        // Aquí puedes manejar los errores
        if (error.error instanceof ErrorEvent) {
          // Errores del lado del cliente o de la red.
          errorMessage = `Error: ${error.error.message}`;
        } else {
          // Errores del lado del servidor.
          switch (error.status) {
            case HTTP_STATUS_BAD_REQUEST:
              errorMessage = 'Solicitud incorrecta. Por favor, verifica los datos del banner.';
              break;
            case HTTP_STATUS_UNAUTHORIZED:
              errorMessage = 'No autorizado. Por favor, verifica tus credenciales.';
              break;
            case HTTP_STATUS_FORBIDDEN:
              errorMessage = 'Prohibido. No tienes permiso para realizar esta acción.';
              break;
            case HTTP_STATUS_NOT_FOUND:
              errorMessage = 'No se encontró el recurso solicitado.';
              break;
            case HTTP_STATUS_INTERNAL_SERVER_ERROR:
              errorMessage = 'Error interno del servidor. Por favor, intenta más tarde.';
              break;
            default:
              errorMessage = `Error del servidor: código ${error.status}, mensaje: ${error.message}`;
              break;
          }
        }
      
        console.error(errorMessage);
        return throwError(() => errorMessage);
      })   
    );
  }
}
