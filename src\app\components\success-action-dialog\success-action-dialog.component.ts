import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { delay, of } from 'rxjs';

@Component({
  selector: 'app-success-action-dialog',
  templateUrl: './success-action-dialog.component.html',
  styleUrl: './success-action-dialog.component.css'
})
export class SuccessActionDialogComponent implements OnInit {
  constructor(
    @Inject(MAT_DIALOG_DATA) private data: {autoCloseDelay?: number},
    private _dialogRef: MatDialogRef<SuccessActionDialogComponent>,
  ) { }

  onClose() {
    this._dialogRef.close(true);
  }

  ngOnInit(): void {
    if(this.data.autoCloseDelay) {
      this._dialogRef.disableClose = true;
      of(true).pipe(delay(this.data.autoCloseDelay)).subscribe(() => {
        this.onClose();
      });
    }
  }
}
