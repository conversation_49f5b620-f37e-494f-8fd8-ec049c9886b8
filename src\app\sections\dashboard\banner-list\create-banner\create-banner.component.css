@import '../../../../shared/styles/common-section-layout.css';

.banner-form {
    display: flex;
    flex-direction: column;
    gap: 22px;
    button {
        width: fit-content;
    }
}

.image-fields {
    display: flex;
    height: 222px;
    justify-content:space-between;
}

.form-field-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;

    p {
        margin: 0px;
        color: #FFF;
        font-family: "Visby CF";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
    }
    
    .form-field-title {
        display: flex;
        gap: 4px;
    }

    .form-field {
        width: 100%;
        height: 50px;
        background-color: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        --mdc-outlined-text-field-input-text-color: white;
        --mat-form-field-container-text-size: 14px;
        --mat-form-field-container-text-line-height: 22px;
        --mat-form-field-container-height: 22px;
        --mat-form-field-container-text-weight: 700;
        --mat-form-field-container-vertical-padding: 14px;
        --as-border-color: transparent;
        --as-border-color-error: #ff003f;
        --mdc-outlined-text-field-hover-outline-color: var(--as-border-color);
        --mdc-outlined-text-field-outline-color: var(--as-border-color);
        --mdc-outlined-text-field-focus-outline-color: var(--as-border-color);

        --mdc-outlined-text-field-caret-color: white;
        --mdc-outlined-text-field-error-caret-color: white;

        border-radius: 12px;
        background-color: #ffffff1f;
        --mdc-outlined-text-field-container-shape: 12px;
        --mdc-outlined-text-field-input-text-placeholder-color: rgb(255, 255, 255, 0.5);

        --mdc-outlined-text-field-error-outline-color: var(--as-border-color-error);

        --mdc-outlined-text-field-error-focus-outline-color: var(--as-border-color-error);

        --mdc-outlined-text-field-error-hover-outline-color: var(--as-border-color-error);

        --mdc-outlined-text-field-outline-width: 2px;

        --mat-form-field-container-text-tracking: 0.14px;

        font-family: Visby CF;
        color: white;

        input::placeholder {
            font-weight: 500;
        }
    }
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 28px;
}

.common-flat-button {
    --mdc-filled-button-label-text-font:"Visby CF";
    --mdc-filled-button-label-text-size:16px;
    --mdc-filled-button-label-text-weight:700;
    --mdc-filled-button-container-height:40px;
    --mdc-filled-button-container-shape:12px;
    --mat-filled-button-horizontal-padding: 32px;
    --mdc-filled-button-label-text-color:#1A191F;
    --mdc-filled-button-container-color:#1BE2ED;
    --mdc-filled-button-label-text-tracking:0.33px;

    &.plain {
        --mdc-filled-button-container-color:#E0E2E7;
    }   
}

.common-flat-button-disabled {
    &:disabled {
        --mdc-filled-button-disabled-label-text-color:#1A191F;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.60) 100%), var(--Aqua, #1BE2ED);
    }
}

.on-hover-flat-button {
    transition: color 0.5s ease, background-color 0.5s ease;
    &:hover {
        --mdc-filled-button-label-text-color:#ffffff;
        --mdc-filled-button-container-color:#FFA01B;
    }
}

.form-field-group {
    display: flex;
    gap: 22px;
}

.position-select {
    max-width: 64px;
}

.position-option {
    color: black;
    background-color: transparent;
}