table {
    width: 100%;
  }
  
  .mat-mdc-form-field {
    font-size: 14px;
    width: 100%;
  }
  
  .empty-row {
    height: 52px;
    width: 100%;
    background: #F7F9FB !important;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    border-top: 1px solid rgba(168, 197, 218, 0.60);
  }
  
  td {
    color: #000;
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    background: #F7F9FB !important;
    gap: 32px;
    padding: 8px 23px;
    border-bottom: 1px solid rgba(168, 197, 218, 0.60);
  }
  
  th {
    color: var(--violet, #550DC5);
    font-family: "Visby CF";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    background: #F7F9FB !important;
    height: 69px;
    padding: 8px 23px;
    gap: 32px;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(168, 197, 218, 0.60);
  }

  .boton-editar {
    border-radius: 12px;
    background: #00E5EF;
    box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.18);
    color: #000;
    text-align: center;
    font-family: "Visby CF";
    font-size: 16px;
    font-style: normal;
    font-weight: 900;
    line-height: normal;
    padding: 12px 24px;
    border: 2px solid #00E5EF;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    cursor: pointer; /* Cambia el icono del mouse a una manita */
  }
  
  .boton-editar:hover, .boton-editar:active {
    background: #04bdc0; /* Cambia el color de fondo */
    border-color: #04bdc0; /* Cambia el color de fondo a negro cuando se pasa sobre el botón o se selecciona */
    color: #000; /* Cambia el color del texto a #00E5EF cuando se pasa sobre el botón o se selecciona */
  }
  

  /* Columna Nombre */
td.mat-column-nombre,
th.mat-column-nombre {
    width: 200px;
    max-width: 300px;
    height: 60px;
    text-align: left;
    height: 20px;
    padding: 8px 10px 8px 0px;
}

/* Columna URL */
td.mat-column-redirect_to,
th.mat-column-redirect_to {
    width: 300px !important; /* Ancho fijo para mantener uniformidad */
    max-width: 500px;
    height: 60px;
    text-align: left;
    height: 20px;
    padding: 8px 24px 8px 0px;
}

/* Estilo para truncar URL */
.url-cell {
    position: relative;
}

.truncated-url {
    display: block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.truncated-url:hover {
    background-color: rgba(85, 13, 197, 0.1);
    color: var(--violet, #550DC5);
}

td.mat-column-posicion,
th.mat-column-posicion {
  width: 60px !important; /* Cambiado de 563px a 595px */
  height: 60px;
  text-align: left;
  height: 20px;
  font-weight: 800;
}

  .mat-column-thumbnail {
    width: 120px;
    max-width: 120px;
    height: 20px;
    padding: 8px 0px 8px 24px;
  }

  /* Name Botones */
  td.mat-column-botones,
  th.mat-column-botones {
      width: 93px;
      height: 49px;
      padding-right: 10px;
  }


  div.mat-elevation-z8 {
    padding-left: 45px;
    box-sizing: border-box;
    background: transparent;
    border: none;
    box-shadow: none;
  }

  .message{
   padding-left: 200px;
  }

.thumbnail-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 86px;
  height: 56px;

  img {
    position: absolute;
    top: 0px;
    left: 0px;
  }

  p,button {
    position: relative;
  }

  p {
    font-family: "Visby CF";
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: 10px;
    color: #ffffff;
    width: 60%;
  }
}

mat-paginator {
  border-top: 1px solid rgba(168, 197, 218, 0.60);
  --mat-paginator-container-background-color:#F7F9FB;
}

.action-button-icon {
  --mdc-icon-button-state-layer-size:32px;
  background-color: #F86060;
  border-radius: 8px;
  padding: 4px;

  ::ng-deep .mat-mdc-button-touch-target {
      height:  var(--mdc-icon-button-state-layer-size);
      width: var(--mdc-icon-button-state-layer-size);
  }
}

.action-button-icon-ext {
  &.mat-mdc-fab.mat-accent {
      --mdc-fab-container-color: #1BE2ED;
      --mat-fab-foreground-color: #201F28;
      &.unfinished-radio {
          --mdc-fab-container-color: #FFD53D;
      }
  }

  --mdc-extended-fab-container-height:32px;
  --mdc-extended-fab-label-text-font:"Visby SemiBold CF";
  --mdc-extended-fab-label-text-weight:700;
  --mdc-extended-fab-container-shape:8px;
  --mdc-extended-fab-container-elevation-shadow:none;
  --mdc-extended-fab-hover-container-elevation-shadow:none;
  --mdc-extended-fab-focus-container-elevation-shadow:none;
  --mdc-extended-fab-pressed-container-elevation-shadow:none;
  --mdc-extended-fab-label-text-tracking: 1px;
  height: var(--mdc-extended-fab-container-height);
  --mat-fab-state-layer-color: #fff;
  --mat-fab-ripple-color: rgba(255, 255, 255, 0.1);

  ::ng-deep .mat-mdc-button-touch-target {
      height:  var(--mdc-extended-fab-container-height);;
  }
}

.actions-column-row {
  display: flex;
  height: 75px;
  justify-content: start;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.on-hover-overlay {
  &::before {
    content: "";
    border-radius:inherit;
    position: absolute;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  &:hover {
      &::before {
          opacity: 1;
      }
  }
}

.on-hover-overlay-icon-button {
  --mat-icon-button-hover-state-layer-opacity: 1;
  --mat-icon-button-focus-state-layer-opacity: 1;
  --mat-icon-button-pressed-state-layer-opacity: 1;
  --mat-icon-button-state-layer-color:transparent;
  ::ng-deep .mat-mdc-button-persistent-ripple.mdc-icon-button__ripple {
      border-radius: inherit;
  }
  ::ng-deep .mat-mdc-button-persistent-ripple.mdc-icon-button__ripple::before {
      border-radius: inherit;
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%);
      transition: opacity 0.5s ease;
  }
}

.estado-column {

  span {
    padding: 8px;
    border-radius: 20px;
    font-weight: 800;
  }

  .publicado{
    background-color: #CDFFCD;
    color: #007F00;
  }

  .inactivo {
    background-color: #EDEEF1;
    color: #000000;
  }

  .activo{
    background-color: #CDFFCD;
    color: #007F00;
  }

  .por-publicar{
    background-color: #FFECCC;
    color: #E05904;
  }

  .error-publicacion {
    background-color: #FFE0E0;
    color: #D30000;
  }
}