import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CustomSnackbarComponent } from '../../../../components/custom-snackbar/custom-snackbar.component';
@Component({
  selector: 'app-image-pick-field',
  templateUrl: './image-pick-field.component.html',
  styleUrl: './image-pick-field.component.css'
})
export class ImagePickFieldComponent {
  constructor(private _snackBar: MatSnackBar) {}
  @Input() asFieldName!:string;
  @Input() asDimensionSpecs!:{width: number, height: number};
  @Output() onFieldValueUpdate = new EventEmitter<{[p:string]:File | null}>();
  @Input() asFormControlName!: string;
  isRadioCoverFieldHovered = false;
  asRadioCover: string | null = null;

  mouseEnter() {
    this.isRadioCoverFieldHovered = true;
  }

  mouseLeave() {
    this.isRadioCoverFieldHovered = false;
  }

  onImagePicked(_file: File) {
    if (_file !== null) {
      let reader = new FileReader();
      reader.onload = (event: any) => {
        this.asRadioCover = event.target.result;
        this.onFieldValueUpdate.emit({[this.asFormControlName]: _file});
      };
      reader.readAsDataURL(_file);
    }
  }

  validateImageDimensions(event: Event) {
    const _target = event.target as HTMLInputElement;
    const file = _target.files ? _target.files[0] : null;
    if(file !== null) {
      let nImg = new Image();
      nImg.onload = () => {
        const widthIsOutOfRange = nImg.width !== this.asDimensionSpecs.width * 2;
        const heightIsOutOfRange = nImg.height !== this.asDimensionSpecs.height * 2;
        if(widthIsOutOfRange || heightIsOutOfRange) {
          this.emitSnackbarMessage("Imagen con proporción inadecuada", true);
        }
        else {
          this.onImagePicked(file);
        }
      };
      nImg.src = URL.createObjectURL(file);
    }
  };

  emitSnackbarMessage(message: string, isError: boolean = false) {
      const SNACKBAR_DURATION = 5;
      this._snackBar.openFromComponent(CustomSnackbarComponent, {
        duration: SNACKBAR_DURATION * 1000,
        horizontalPosition: "center",
        verticalPosition: "top",
        data: { message },
        panelClass: isError ? ["error-snackbar"] : ['success-snackbar'],
      });
    }
}
