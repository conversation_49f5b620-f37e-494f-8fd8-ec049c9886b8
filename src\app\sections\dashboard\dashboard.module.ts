import { NgModule } from '@angular/core';
import { AsyncPipe, CommonModule } from '@angular/common';
import { NgOptimizedImage } from '@angular/common';
import { DashboardComponent } from './dashboard.component';
import { HeaderComponent } from './header/header.component';
import { OverviewComponent } from './overview/overview.component';
import { SidebarComponent } from './sidebar/sidebar.component';
import { DashboardRoutingModule } from './dashboard-routing/dashboard-routing.module';
import { ReactiveFormsModule } from '@angular/forms';
import { ComponentsModule } from '../../components/components.module';
import { MatGridListModule } from '@angular/material/grid-list';
import { LayoutModule } from '@angular/cdk/layout';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatPaginatorIntl, MatPaginatorModule } from '@angular/material/paginator';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { CustomPaginatorIntl } from './custom-paginator-intl';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatMenuModule } from '@angular/material/menu';
import { MatRadioModule } from '@angular/material/radio';
import { ReproductionsComponent } from './reproductions/reproductions.component';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatMultiSortModule } from 'ngx-mat-multi-sort';
import { AlbumDetailComponent } from './album-detail/album-detail.component';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { CargaCancionesComponent } from './carga-canciones/carga-canciones.component';
import { PlayListComponent } from './play-list/play-list.component';
import { TableOverviewExample } from './play-list/table-playlist/table-playlist.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ActualizarPlaylistComponent } from './play-list/actualizar-playlist/actualizar-playlist.component';
import { FormsModule } from '@angular/forms';
import {MatChipsModule} from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { BannerListComponent } from './banner-list/banner-list.component';
import { TableBannerlistComponent } from './banner-list/table-bannerlist/table-bannerlist.component';
import{ ActualizarBannerComponent } from './banner-list/actualizar-banner/actualizar-banner.component';
import { ImagePickFieldComponent } from './banner-list/image-pick-field/image-pick-field.component';
import { CreateBannerComponent } from './banner-list/create-banner/create-banner.component';
import { RadioListComponent } from './radio/components/radio-list/radio-list.component';
import { CreateRadioComponent } from './radio/components/create-radio/create-radio.component';
import { UpdateRadioComponent } from './radio/components/update-radio/update-radio.component';
import { RadioConfigTabsFormComponent } from './radio/components/radio-config-tabs-form/radio-config-tabs-form.component';
import { RadioFinishProcessDialogComponent } from './radio/components/radio-finish-process-dialog/radio-finish-process-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { LookupConfigItemComponent } from './radio/components/lookup-config-item/lookup-config-item.component';
import { PublishRadioConfirmDialogComponent } from './radio/components/publish-radio-confirm-dialog/publish-radio-confirm-dialog.component';
import { ArtistReproductionsComponent } from './artist-reproductions/artist-reproductions.component';
import { RecordLabelReproductionsComponent } from './record-label/record-label-reproductions/record-label-reproductions.component';

@NgModule({
  imports: [
    CommonModule,
    DashboardRoutingModule,
    ReactiveFormsModule,
    MatGridListModule,
    LayoutModule,
    MatIconModule,
    MatFormFieldModule,
    MatTableModule,
    MatMultiSortModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatDatepickerModule,
    MatCardModule,
    MatMenuModule,
    MatRadioModule,
    MatButtonModule,
    MatDividerModule,
    MatAutocompleteModule,
    MatSelectModule,
    AsyncPipe,
    ComponentsModule,
    NgOptimizedImage,
    FormsModule,
    MatChipsModule,
    MatTabsModule,
    MatDialogModule,
    MatTooltipModule,
    ClipboardModule
  ],
  declarations: [
    DashboardComponent,
    SidebarComponent,
    HeaderComponent,
    OverviewComponent,
    ReproductionsComponent,
    CargaCancionesComponent,
    AlbumDetailComponent,
    PlayListComponent,
    TableOverviewExample,
    ActualizarPlaylistComponent,
    BannerListComponent,
    TableBannerlistComponent,
    ActualizarBannerComponent,
    CreateBannerComponent,
    ImagePickFieldComponent,
    RadioListComponent,
    RadioConfigTabsFormComponent,
    CreateRadioComponent,
    UpdateRadioComponent,
    RadioFinishProcessDialogComponent,
    LookupConfigItemComponent,
    PublishRadioConfirmDialogComponent,
    ArtistReproductionsComponent,
    RecordLabelReproductionsComponent
  ],
  exports: [
    PlayListComponent,
    TableOverviewExample,
    ActualizarPlaylistComponent,
    BannerListComponent,
    TableBannerlistComponent,
    ActualizarBannerComponent,
    RadioListComponent,
    CreateRadioComponent,
    RadioConfigTabsFormComponent,
    UpdateRadioComponent,
    PublishRadioConfirmDialogComponent,
    ArtistReproductionsComponent
  ],
  providers:[
    { provide: MatPaginatorIntl, useClass: CustomPaginatorIntl},
    { provide: MAT_DATE_LOCALE, useValue: 'es' },
  ]
})
export class DashboardModule { }
