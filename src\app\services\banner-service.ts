import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { BannerState } from '../enums/banner-state.enum'

// Definición de la interfaz Banner
export interface Banner {
  id: number;
  nombre: string;
  urlbanner: string;
  urlbannermob: string;
  redirect_to: string;
  fechacreacion: string;
  banner_text?: string;
  banner_action_text?: string;
  position: number;
  state: BannerState;
  pubication_date?: string;
}

// Definición de constantes para los códigos de estado HTTP
const HTTP_STATUS_BAD_REQUEST = 400;
const HTTP_STATUS_UNAUTHORIZED = 401;
const HTTP_STATUS_FORBIDDEN = 403;
const HTTP_STATUS_NOT_FOUND = 404;
const HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;

@Injectable({
  providedIn: 'root'
})
export class BannerService {
    
  private url = `${environment.apiUrl}/banner/active-pending`;

  constructor(private http: HttpClient) { }

  // Uso de la interfaz Banner para definir el tipo de retorno
  getBanners(): Observable<HttpResponse<Banner[]>> {
    return this.http.get<Banner[]>(this.url, { observe: 'response' }).pipe(
      tap(response => {
        if (response.status === 200) {
          console.log('Exito obteniendo los banners del servidor:', response.body);
        }
      }),
      catchError(error => {
        let errorMessage = 'Ha ocurrido un error desconocido';
        
        if (error.error instanceof ErrorEvent) {
          // Errores del lado del cliente o de la red.
          errorMessage = `Error: ${error.error.message}`;
        } else {
          // Errores del lado del servidor.
          switch (error.status) {
            case HTTP_STATUS_BAD_REQUEST:
              errorMessage = 'Solicitud incorrecta. Por favor, verifica los datos del archivo.';
              break;
            case HTTP_STATUS_UNAUTHORIZED:
              errorMessage = 'No autorizado. Por favor, verifica tus credenciales.';
              break;
            case HTTP_STATUS_FORBIDDEN:
              errorMessage = 'Prohibido. No tienes permiso para realizar esta acción.';
              break;
            case HTTP_STATUS_NOT_FOUND:
              errorMessage = 'No se encontró el recurso solicitado.';
              break;
            case HTTP_STATUS_INTERNAL_SERVER_ERROR:
              errorMessage = 'Error interno del servidor. Por favor, intenta más tarde.';
              break;
            default:
              errorMessage = `Error del servidor: código ${error.status}, mensaje: ${error.message}`;
              break;
          }
        }
        
        console.error(errorMessage);
        return throwError(() => errorMessage);
      })
    );
  }

  deleteBanner(bannerId: number): Observable<HttpResponse<any>> {
    const url = `${environment.apiUrl}/banner/${bannerId}`;
    return this.http.delete(url, { observe: 'response' }).pipe(
      tap(response => {
        if (response.status === 200) {
          console.log('Exito eliminando el banner del servidor:', response.body);
        }
      }),
      catchError(error => {
        let errorMessage = 'Ha ocurrido un error desconocido';
        
        if (error.error instanceof ErrorEvent) {
          // Errores del lado del cliente o de la red.
          errorMessage = `Error: ${error.error.message}`;
        } else {
          // Errores del lado del servidor.
          switch (error.status) {
            case HTTP_STATUS_BAD_REQUEST:
              errorMessage = 'Solicitud incorrecta. Por favor, verifica los datos del archivo.';
              break;
            case HTTP_STATUS_UNAUTHORIZED:
              errorMessage = 'No autorizado. Por favor, verifica tus credenciales.';
              break;
            case HTTP_STATUS_FORBIDDEN:
              errorMessage = 'Prohibido. No tienes permiso para realizar esta acción.';
              break;
            case HTTP_STATUS_NOT_FOUND:
              errorMessage = 'No se encontró el recurso solicitado.';
              break;
            case HTTP_STATUS_INTERNAL_SERVER_ERROR:
              errorMessage = 'Error interno del servidor. Por favor, intenta más tarde.';
              break;
            default:
              errorMessage = `Error del servidor: código ${error.status}, mensaje: ${error.message}`;
              break;
          }
        }
        
        console.error(errorMessage);
        return throwError(() => errorMessage);
      })
    );
  }
}
