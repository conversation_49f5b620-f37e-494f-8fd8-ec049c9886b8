<mat-dialog-content>
    <div class="as-header">
        <h4>Programar Banner</h4>
        <mat-icon class="material-symbols-outlined" (click)="onClose()">close</mat-icon>
    </div>
    <div class="as-description">
        <p style="font-size: 18px; line-height: 24px; margin-bottom: 24px;">Selecciona la fecha y hora de publicación</p>
        
        <form [formGroup]="scheduleForm" class="schedule-form">
            <div class="form-field-container">
                <p>Fecha de publicación*</p>
                <mat-form-field appearance="outline" class="form-field">
                    <input matInput [matDatepicker]="picker" formControlName="date" placeholder="Seleccionar fecha">
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-error *ngIf="scheduleForm.get('date')?.hasError('required')">
                        La fecha es requerida
                    </mat-error>
                    <mat-error *ngIf="scheduleForm.get('date')?.hasError('pastDate')">
                        La fecha debe ser mayor a la actual
                    </mat-error>
                </mat-form-field>
            </div>

            <div class="form-field-container">
                <p>Hora de publicación*</p>
                <mat-form-field appearance="outline" class="form-field">
                    <input matInput type="time" formControlName="time" placeholder="Seleccionar hora">
                    <mat-error *ngIf="scheduleForm.get('time')?.hasError('required')">
                        La hora es requerida
                    </mat-error>
                    <mat-error *ngIf="scheduleForm.get('time')?.hasError('pastTime')">
                        La hora debe ser mayor a la actual para la fecha de hoy
                    </mat-error>
                </mat-form-field>
            </div>
        </form>
    </div>
</mat-dialog-content>
<mat-dialog-actions class="as-actions">
    <button mat-flat-button class="common-flat-button on-hover-flat-button plain" (click)="onCancel()">Cancelar</button>
    <button mat-flat-button class="common-flat-button on-hover-flat-button" (click)="onPublish()" [disabled]="!scheduleForm.valid">Programar</button>
</mat-dialog-actions>
