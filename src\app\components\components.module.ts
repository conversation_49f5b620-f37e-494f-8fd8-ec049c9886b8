import { NgModule } from '@angular/core';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { FooterContentComponent } from './footer-content/footer-content.component';
import { MatIconModule } from '@angular/material/icon';
import { CustomCalendarHeaderComponent } from './custom-calendar-header/custom-calendar-header.component';
import { MatButtonModule } from '@angular/material/button';
import { CustomSnackbarComponent } from './custom-snackbar/custom-snackbar.component';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MultitargetSearchDialogComponent } from './multitarget-search-dialog/multitarget-search-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ActionConfirmDialogComponent } from './action-confirm-dialog/action-confirm-dialog.component';
import { SuccessActionDialogComponent } from './success-action-dialog/success-action-dialog.component';
import { CustomSnackbarV2Component } from './custom-snackbar-v2/custom-snackbar-v2.component';
import { ProcessFeedbackDialogComponent } from './process-feedback-dialog/process-feedback-dialog.component';
import { DeleteConfirmDialogComponent } from './delete-confirm-dialog/delete-confirm-dialog.component';



@NgModule({
  declarations: [
    FooterContentComponent,
    CustomCalendarHeaderComponent,
    CustomSnackbarComponent,
    MultitargetSearchDialogComponent,
    ActionConfirmDialogComponent,
    SuccessActionDialogComponent,
    CustomSnackbarV2Component,
    ProcessFeedbackDialogComponent,
    DeleteConfirmDialogComponent
  ],
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatSnackBarModule,
    MatDialogModule,
    NgOptimizedImage,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    MatInputModule,
    MatDialogModule
  ],
  exports:[
    FooterContentComponent,
    CustomCalendarHeaderComponent,
    ActionConfirmDialogComponent,
    SuccessActionDialogComponent,
    ProcessFeedbackDialogComponent,
    DeleteConfirmDialogComponent
  ]
})
export class ComponentsModule { }
