<div class="mat-elevation-z8" style="width: 90%; margin: auto">
  <div style="border-radius: 10px; overflow: hidden;">
    <table mat-table [dataSource]="dataSource" matSort>

      <ng-container matColumnDef="posicion">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Posición </th>
        <td mat-cell *matCellDef="let row"> {{row.position}} </td>
      </ng-container>

      <ng-container matColumnDef="thumbnail">
        <th mat-header-cell *matHeaderCellDef> Thumbnail </th>
        <td mat-cell *matCellDef="let row">
          <div class="thumbnail-container">
            <img src="{{row.urlbannermob}}" width="86" height="56"/>
          </div>
        </td>
      </ng-container>
      
      <ng-container matColumnDef="nombre">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Título </th>
        <td mat-cell *matCellDef="let row"> {{row.nombre}} </td>
      </ng-container>
      
      <ng-container matColumnDef="redirect_to">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> URL redirección </th>
        <td mat-cell *matCellDef="let row" class="url-cell">
          <span class="truncated-url" matTooltip="{{row.redirect_to}} " matTooltipPosition="above" (click)="copyToClipboard(row.redirect_to)">{{row.redirect_to}}</span>
        </td>
      </ng-container>

      <ng-container matColumnDef="estado">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Estado </th>
        <td mat-cell *matCellDef="let row" class="estado-column"> 
          @switch (row.state) {
            @case (BannerState.INACTIVE) {
              <span class="inactivo">Inactivo</span>
            }
            @case (BannerState.ACTIVE) {
              <span class="publicado">Publicado</span>
            }
            @case (BannerState.PENDING) {
              <span class="por-publicar">Por Publicar</span>
            }
            @case (BannerState.ERROR) {
              <span class="error-publicacion">Error en publicación</span>
            }
            @default {
              <span class="inactivo">Inactivo</span>
            }
          }
        </td>
      </ng-container>

      <ng-container matColumnDef="acciones">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Acciones </th>
        <td mat-cell *matCellDef="let row" class="actions-column-row"> 
          <button mat-icon-button (click)="onDeleteClick(row.id)" class="action-button-icon">
            <mat-icon>delete_outline</mat-icon>
          </button>
          
          <button mat-fab extended class="action-button-icon-ext on-hover-overlay-icon-button" *ngIf="row.state != BannerState.ERROR" (click)="onEditarClick(row.id)">
            <mat-icon>edit_note_outline</mat-icon>
            editar
          </button>

          <button mat-fab extended class="action-button-icon-ext unfinished-radio on-hover-overlay" *ngIf="row.state == BannerState.ERROR" (click)="onEditarClick(row.id)">
            <mat-icon>edit_note_outline</mat-icon>
            Publicar
          </button>
        </td>
      </ng-container>
      
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      <tr class="mat-row" *matNoDataRow>
      </tr>
    </table>
    <div class="empty-row"></div>
    <!-- add one extra empty row -->
    
  </div>
</div>