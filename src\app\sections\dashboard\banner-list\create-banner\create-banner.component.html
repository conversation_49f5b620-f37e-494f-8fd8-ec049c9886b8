<div class="section-header">
    <p>Banners</p>
</div>
<div class="section-content">
    <form [formGroup]="createBannerForm" (ngSubmit)="onSubmit($event)" class="banner-form">
        <div class="image-fields">
            <app-image-pick-field asFieldName='Cover Móvil @x2' [asDimensionSpecs]="{width:361,height:240}" (onFieldValueUpdate)="imageFieldUpdate($event)" asFormControlName="bannerMob"/>
            <app-image-pick-field asFieldName='Cover Desktop Mediano @x2' [asDimensionSpecs]="{width:1200,height:320}" (onFieldValueUpdate)="imageFieldUpdate($event)" asFormControlName="bannerWeb"/>
            <app-image-pick-field asFieldName='Cover Desktop Ancho @x2' [asDimensionSpecs]="{width:1800,height:420}" (onFieldValueUpdate)="imageFieldUpdate($event)" asFormControlName="bannerWebExt"/>
        </div>
        <div class="form-field-container">
            <div class="form-field-title">
                <p>Título</p>
            </div>
            <mat-form-field appearance="outline" class="form-field" subscriptSizing="dynamic">
                <input matInput [formControl]="createBannerForm.controls.nombre" placeholder="Ingresa nombre de imagen"
                    maxlength="22" />
            </mat-form-field>
        </div>
        <div class="form-field-container">
            <div class="form-field-title">
                <p>URL de la imagen</p>
            </div>
            <mat-form-field class="form-field" appearance="outline" subscriptSizing="dynamic">
                <input matInput [formControl]="createBannerForm.controls.url" placeholder="Ingresa URL de la imagen"/>
            </mat-form-field>
        </div>
        <div class="form-field-container">
            <div class="form-field-title">
                <p>Texto principal (máximo 60 caracteres)</p>
            </div>
            <mat-form-field appearance="outline" class="form-field" subscriptSizing="dynamic">
                <input matInput [formControl]="createBannerForm.controls.texto_banner"
                    placeholder="Ingresa el texto principal" maxlength="60" />
            </mat-form-field>
        </div>
        <div class="form-field-container">
            <div class="form-field-title">
                <p>Texto del botón (máximo 20 caracteres)</p>
            </div>
            <mat-form-field class="form-field" appearance="outline" subscriptSizing="dynamic">
                <input matInput [formControl]="createBannerForm.controls.texto_boton_banner"
                    placeholder="Ingresa el texto del botón" maxlength="20" />
            </mat-form-field>
        </div>
        <div class="form-actions">
            <button mat-flat-button class="common-flat-button plain on-hover-flat-button" type="button" (click)="onProcessStop()">
                Cancelar
            </button>
            <button mat-flat-button class="common-flat-button on-hover-flat-button common-flat-button-disabled" type="submit" [disabled]="!createBannerForm.valid">
                Guardar
            </button>
        </div>
    </form>
</div>