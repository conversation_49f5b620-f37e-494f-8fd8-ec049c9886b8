import { Component, EventEmitter, Inject, Output } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

interface DialogData {
  message?: string;
  action?: string;
  title?: string;
}

@Component({
  selector: 'app-delete-confirm-dialog',
  templateUrl: './delete-confirm-dialog.component.html',
  styleUrl: './delete-confirm-dialog.component.css'
})
export class DeleteConfirmDialogComponent {
  message: string;
  action: string;
  title: string;

  constructor(
    private _dialogRef: MatDialogRef<DeleteConfirmDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    console.log(data);
    this.message = data?.message || '¿Estás seguro que deseas eliminar este banner?';
    this.action = data?.action || 'Eliminar';
    this.title = data?.title || 'Eliminar Banner';
  }

  @Output() onActionClick = new EventEmitter<boolean>();

  actionClickHandle(confirmed: boolean) {
    this.onActionClick.emit(confirmed);
    this.onClose();
  }

  onClose() {
    this._dialogRef.close(false);
  }
}
