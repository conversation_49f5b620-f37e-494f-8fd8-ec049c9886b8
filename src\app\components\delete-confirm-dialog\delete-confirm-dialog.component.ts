import { Component, EventEmitter, Output } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-delete-confirm-dialog',
  templateUrl: './delete-confirm-dialog.component.html',
  styleUrl: './delete-confirm-dialog.component.css'
})
export class DeleteConfirmDialogComponent {
  constructor(
    private _dialogRef: MatDialogRef<DeleteConfirmDialogComponent>,
  ) {}

  @Output() onActionClick = new EventEmitter<boolean>();

  actionClickHandle(confirmed: boolean) {
    this.onActionClick.emit(confirmed);
    this.onClose();
  }

  onClose() {
    this._dialogRef.close(false);
  }
}
