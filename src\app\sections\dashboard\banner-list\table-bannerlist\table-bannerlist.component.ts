import {
  AfterViewInit,
  Component,
  ViewChild,
  Output,
  EventEmitter,
} from "@angular/core"
import { MatPaginator } from "@angular/material/paginator"
import { MatSort } from "@angular/material/sort"
import { MatTableDataSource } from "@angular/material/table"
import { BannerService, Banner } from "../../../../services/banner-service"
import { CustomSnackbarComponent } from "../../../../components/custom-snackbar/custom-snackbar.component"
import { MatSnackBar } from "@angular/material/snack-bar"
import { Clipboard } from "@angular/cdk/clipboard"
import { BannerState } from "../../../../enums/banner-state.enum"
import { MatDialog } from "@angular/material/dialog"
import { DeleteConfirmDialogComponent } from "../../../../components/delete-confirm-dialog/delete-confirm-dialog.component"
import { RoutingService } from "../../../../services/routing-service"

@Component({
  selector: "app-table-bannerlist",
  templateUrl: "./table-bannerlist.component.html",
  styleUrls: ["./table-bannerlist.component.css"],
})
export class TableBannerlistComponent implements AfterViewInit {
  // Declara el EventEmitter
  @Output() editarClicked = new EventEmitter<{
    editar: boolean
    bannerId: string
  }>()

  displayedColumns: string[] = [
    "posicion",
    "thumbnail",
    "nombre",
    "redirect_to",
    "estado",
    "acciones",
  ]
  dataSource: MatTableDataSource<Banner>

  @ViewChild(MatPaginator) paginator!: MatPaginator
  @ViewChild(MatSort) sort!: MatSort

  BannerState = BannerState
  
  constructor(
    private bannerService: BannerService,
    private _snackBar: MatSnackBar,
    private clipboard: Clipboard,
    private dialog: MatDialog,
    private routingService: RoutingService
  ) {
    this.dataSource = new MatTableDataSource<Banner>([])
  }

  durationInSeconds = 10

  ngAfterViewInit() {
    this.bannerService.getBanners().subscribe({
      next: (response) => {
        if (response.status === 200 && response.body) {
          this.dataSource.data = response.body.map((banner, index) => ({
            id: banner.id,
            nombre: banner.nombre,
            urlbanner: banner.urlbanner,
            urlbannermob: banner.urlbannermob,
            urlbannerext: banner.urlbannerext,
            redirect_to: banner.redirect_to,
            fechacreacion: banner.fechacreacion,
            banner_text: banner.banner_text,
            banner_action_text: banner.banner_action_text,
            botones: "",
            position: banner.position,
            state: banner.state,
          }))
        }
      },
      error: (err) => {
        // Aquí es donde manejas el error.
        console.error("Hubo un error al obtener los banners:", err)
        this._snackBar.openFromComponent(CustomSnackbarComponent, {
          duration: this.durationInSeconds * 1000,
          horizontalPosition: "center",
          verticalPosition: "top",
          data: { message: err },
          panelClass: ["error-snackbar"],
        })
      },
      complete: () => {
        this.dataSource.paginator = this.paginator
        this.dataSource.sort = this.sort
      },
    })
  }
  
  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value
    this.dataSource.filter = filterValue.trim().toLowerCase()
    
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage()
    }
  }
  
  copyToClipboard(url: string) {
    const success = this.clipboard.copy(url)
    
    if (success) {
      this._snackBar.openFromComponent(CustomSnackbarComponent, {
        duration: 5 * 1000,
        horizontalPosition: "center",
        verticalPosition: "top",
        data: { message: "URL copiada al portapapeles" },
        panelClass: ["success-snackbar"],
      })
    } else {
      this._snackBar.openFromComponent(CustomSnackbarComponent, {
        duration: 5 * 1000,
        horizontalPosition: "center",
        verticalPosition: "top",
        data: { message: "Error al copiar la URL" },
        panelClass: ["error-snackbar"],
      })
    }
  }
  
  onDeleteClick(bannerId: number) {
    // Skip confirmation for empty row (id = 0)
    if (bannerId === 0) {
      return
    }

    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      panelClass: "delete-confirm-dialog-container",
    })
    
    dialogRef.componentInstance.onActionClick.subscribe(
      (confirmed: boolean) => {
        if (confirmed) {
          this.deleteBanner(bannerId)
        }
      }
    )
  }
  
  private deleteBanner(bannerId: number) {
    this.bannerService.deleteBanner(bannerId).subscribe({
      next: (response) => {
        if (response.status === 200) {
          this._snackBar.openFromComponent(CustomSnackbarComponent, {
            duration: 5 * 1000,
            horizontalPosition: "center",
            verticalPosition: "top",
            data: { message: "Banner eliminado con éxito" },
            panelClass: ["success-snackbar"],
          })
          this.ngAfterViewInit()
        }
      },
      error: (err) => {
        // Aquí es donde manejas el error.
        console.error("Hubo un error al eliminar el banner:", err)
        this._snackBar.openFromComponent(CustomSnackbarComponent, {
          duration: this.durationInSeconds * 1000,
          horizontalPosition: "center",
          verticalPosition: "top",
          data: { message: err },
          panelClass: ["error-snackbar"],
        })
      },
    })
  }
  onEditarClick(bannerId: string) {
    this.routingService.add(`dashboard/banner-list/update/${bannerId}`)
  }
}
