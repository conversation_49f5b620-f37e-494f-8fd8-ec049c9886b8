import { Component, Input, OnInit, ViewChild, ElementRef  } from '@angular/core';
import { FormControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { environment } from '../../../../../environments/environment';
import { CrearBannerService } from '../../../../services/crearbanner-service';
import { ActivatedRoute } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CustomSnackbarComponent } from '../../../../components/custom-snackbar/custom-snackbar.component';
@Component({
  selector: 'app-actualizar-banner',
  templateUrl: './actualizar-banner.component.html',
  styleUrl: './actualizar-banner.component.css'
})
export class ActualizarBannerComponent implements OnInit {
    @Input() modoCrearBanner: boolean = false;
    @Input() bannerId: string = '';

    texto = 'Banners';

    bannerForm: FormGroup = this.fb.group({
      bannerTitle: ['', [Validators.required,Validators.maxLength(60)]],
      bannerUrl: ['', [Validators.required, this.isValidUrl]],
      banner_text: ['',Validators.maxLength(60)],
      banner_action_text: ['', [Validators.pattern('^[a-zA-Z\\s]*$'),Validators.maxLength(20)]],
    });

    modo: string = '';
    formSubmitted = false;

    fileNameMobile: string = '';
    fileNameWeb: string = '';

    iconoMobile = 'banner_placeholder_icon';
    iconoWeb = 'banner_placeholder_icon';
    mostrarTextoIconoMobile = false;
    mostrarTextoIconoWeb = false;


    mostrarTextoIcono: boolean = false;

    icono = 'bannerimagen_icon';
    bannerTitleValue = '';
    bannerUrlValue = '';
    bannerImageMobile: string = '';
    bannerImageWeb: string = '';
    imageLoadedMobile: boolean = false;
    imageLoadedWeb: boolean = false;
    showInputs: boolean = true;
    iconoUrlBanner = 'link-playlist_icon';
    urlBanner = `${environment.apiUrl}/banner`;


    constructor(
      private fb: FormBuilder,
      private CrearBannerService: CrearBannerService,
      private route: ActivatedRoute,
      private _snackBar: MatSnackBar,
    ) { 
      if(this.route.snapshot.params["banner_id"]) {
        this.bannerId = this.route.snapshot.params["banner_id"];
        this.modoCrearBanner  = false;
      }else {
        this.modoCrearBanner = true;
      }
    }

    @ViewChild('bannerTitle') bannerTitleInput!: ElementRef;
    @ViewChild('bannerUrl') urlbannerInput!: ElementRef;
        
    ngOnInit() {
      if (this.modoCrearBanner) {
        this.modo = 'Crear banner';
      }
    }

    isValidUrl(control: FormControl): {[s: string]: boolean} | null {
      const urlPattern = /^(http|https):\/\/[^ "]+$/;
      const dotPattern = /\./g;
      const dotCount = (control.value.match(dotPattern) || []).length;
      if (!control.value.match(urlPattern) || dotCount < 1) {
        return { 'invalidUrl': true };
      }
      return null;
    }
    
    emitSnackbarMessage(message: string, isError: boolean = false) {
      const SNACKBAR_DURATION = 5;
      this._snackBar.openFromComponent(CustomSnackbarComponent, {
        duration: SNACKBAR_DURATION * 1000,
        horizontalPosition:"center",
        verticalPosition:"top",
        data:{ message },
        panelClass:isError ? ['error-snackbar']:[],
      });
    }

    onSubmit(event: Event) {
      event.preventDefault();
    
        this.formSubmitted = true;
        let fileInputMobile = document.getElementById('imageUploadMobile') as HTMLInputElement;
        let fileMobile: File = fileInputMobile.files ? fileInputMobile.files[0] : new File([], '');
        if (!fileMobile || fileMobile.size === 0) {
            this.imageLoadedMobile = false;
            return;
        }
        let fileInputWeb = document.getElementById('imageUploadWeb') as HTMLInputElement;
        let fileWeb: File = fileInputWeb.files ? fileInputWeb.files[0] : new File([], '');
        if (!fileWeb || fileWeb.size === 0) {
            this.imageLoadedWeb = false;
            return;
        }
        const { bannerTitle,bannerUrl, banner_text, banner_action_text } = this.bannerForm.value;
        let banner = {
          nombre: bannerTitle,
          url: bannerUrl,
          texto_banner: banner_text,
          texto_boton_banner: banner_action_text,
        };
        /*this.CrearBannerService.createBanner(banner, fileMobile, fileWeb).subscribe({
          next: (response) => {
            this.showInputs = false;
            if (response.body && response.body.banner && response.body.banner.banner_id) {
              this.bannerId = response.body.banner.banner_id.toString(); // Actualiza bannerId con el ID del banner recién creado
              this.urlBanner = this.urlBanner + this.bannerId;
            }
          },
          error: (error) => {
            console.error('Error creando el banner:', error);
          }
        });*/
    }


    onInputChange() {
      this.icono = 'editplaylist_icon';
      this.mostrarTextoIcono = true;
    }

    handleFileInputMobile(event: Event) {
      let target = event.target as HTMLInputElement;
      let files = target.files;
      if (files) {
          let nImg = new Image();
          nImg.onload = () => {
            const widthRatioDiff = Math.abs(1-nImg.width/360);
            const heightRatioDiff = Math.abs(1-nImg.height/248);
            if(widthRatioDiff < 0.2 && heightRatioDiff < 0.2) {
              this.processFilesMobile(files!.item(0) as File);
            }
            else {
              this.emitSnackbarMessage("Imagen móvil con proporción inadecuada",true);
            }
          };
          nImg.src = URL.createObjectURL(files[0]);
      }
    }

    processFilesMobile(_file: File) {
      let file = _file;
      if (file) {
        let reader = new FileReader();
        reader.onload = (event: any) => {
          this.bannerImageMobile = event.target.result;
          this.fileNameMobile = _file.name;
          this.imageLoadedMobile = true; 
        }
        reader.readAsDataURL(file);
      }
    }

    handleFileInputWeb(event: Event) {
      let target = event.target as HTMLInputElement;
      let files = target.files;
      if (files) {
          let nImg = new Image();
          nImg.onload = () => {
            const widthRatioDiff = Math.abs(1 - nImg.width/1230);
            const heightRatioDiff = Math.abs(1 - nImg.height/344);
            if(widthRatioDiff < 0.2 && heightRatioDiff < 0.2) {
              this.processFilesWeb(files![0] as File);
            }
            else {
              this.emitSnackbarMessage("Imagen web con proporción inadecuada",true);
            }
          };
          nImg.src = URL.createObjectURL(files[0]);
      }
    }

    processFilesWeb(_file: File) {
      let file = _file;
      if (file) {
        let reader = new FileReader();
        reader.onload = (event: any) => {
          this.bannerImageWeb = event.target.result;
          this.fileNameWeb = _file.name;
          this.imageLoadedWeb = true; 
        }
        reader.readAsDataURL(file);
      }
    }
    
    
    onDragOverMobile(event: DragEvent) {
      event.preventDefault();
      // Puedes agregar lógica aquí para cambiar el estilo del div cuando se arrastra un archivo sobre él
    }
    
    onDragLeaveMobile(event: DragEvent) {
      event.preventDefault();
      // Puedes agregar lógica aquí para revertir los cambios de estilo cuando el archivo deja de arrastrarse sobre el div
    }
    
    onDropMobile(event: DragEvent) {
      event.preventDefault();
      let files = event.dataTransfer?.files;
      if (files) {
        this.processFilesMobile(files[0]);
      }
    }

    onDragOverWeb(event: DragEvent) {
      event.preventDefault();
      // Puedes agregar lógica aquí para cambiar el estilo del div cuando se arrastra un archivo sobre él
    }
    
    onDragLeaveWeb(event: DragEvent) {
      event.preventDefault();
      // Puedes agregar lógica aquí para revertir los cambios de estilo cuando el archivo deja de arrastrarse sobre el div
    }
    
    onDropWeb(event: DragEvent) {
      event.preventDefault();
      let files = event.dataTransfer?.files;
      if (files) {
        this.processFilesWeb(files[0]);
      }
    }
    
    // Método para activar el ícono y el texto
    activateIcon(): void {
        this.icono = 'editplaylist_icon';
        this.mostrarTextoIcono = true;
    }


    activateIconMobile(): void {
      this.iconoMobile = 'editplaylist_icon';
      this.mostrarTextoIconoMobile = true;
    }


    activateIconWeb(): void {
      this.iconoWeb = 'editplaylist_icon';
      this.mostrarTextoIconoWeb = true;
    }
}
